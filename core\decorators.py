"""
Custom decorators for Tadamon project
"""
from functools import wraps
from django.contrib.auth import logout
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect
from django.contrib import messages


def regular_user_required(view_func):
    """
    Decorator that ensures user is logged in and not from admin session
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        # First check if user is authenticated
        if not request.user.is_authenticated:
            return redirect('login')
        
        # Check if this is an admin session
        if request.session.get('admin_login', False):
            logout(request)
            messages.info(request, 'يرجى تسجيل الدخول مرة أخرى للوصول إلى النظام العادي.')
            return redirect('login')
        
        return view_func(request, *args, **kwargs)
    
    return _wrapped_view


def permission_required_custom(permission):
    """
    Custom permission decorator that works with our permission system
    """
    def decorator(view_func):
        @wraps(view_func)
        @regular_user_required
        def _wrapped_view(request, *args, **kwargs):
            from .views import check_user_permissions
            
            user_permissions = check_user_permissions(request.user)
            
            if not user_permissions.get(permission, False):
                messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه الصفحة.')
                return redirect('dashboard')
            
            return view_func(request, *args, **kwargs)
        
        return _wrapped_view
    
    return decorator


def staff_required(view_func):
    """
    Decorator that requires staff status
    """
    @wraps(view_func)
    @regular_user_required
    def _wrapped_view(request, *args, **kwargs):
        if not (request.user.is_staff or request.user.is_superuser):
            messages.error(request, 'هذه الصفحة متاحة للموظفين فقط.')
            return redirect('dashboard')
        
        return view_func(request, *args, **kwargs)
    
    return _wrapped_view
