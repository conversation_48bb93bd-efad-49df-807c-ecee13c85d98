"""
Custom middleware for <PERSON>amon project
"""
from django.contrib.auth import logout
from django.shortcuts import redirect
from django.urls import reverse
from django.utils.deprecation import MiddlewareMixin


class AdminSessionSeparationMiddleware(MiddlewareMixin):
    """
    Middleware to completely separate admin sessions from regular user sessions
    """

    def process_request(self, request):
        # Skip for static files and media
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return None

        # Check if user is accessing admin
        if request.path.startswith('/admin/'):
            # Mark this as an admin session and store session type
            request.session['session_type'] = 'admin'
            request.session['admin_login'] = True

            # If user was previously in regular session, clear regular session data
            if request.session.get('regular_login', False):
                self._clear_regular_session_data(request)

            return None

        # Check if user is accessing regular app
        if not request.path.startswith('/admin/'):
            # If user has admin session and tries to access regular app
            if request.session.get('admin_login', False) or request.session.get('session_type') == 'admin':
                # Force logout and clear all session data
                self._force_logout_and_clear_session(request)

                # Don't redirect if already on login/logout pages
                if request.path not in [reverse('login'), reverse('logout')]:
                    return redirect('login')

            # Mark as regular session if user is authenticated
            if request.user.is_authenticated:
                request.session['session_type'] = 'regular'
                request.session['regular_login'] = True
                # Clear any admin flags
                request.session.pop('admin_login', None)

        return None

    def _clear_regular_session_data(self, request):
        """Clear regular session data when switching to admin"""
        keys_to_remove = [key for key in request.session.keys()
                         if not key.startswith('_') and key not in ['admin_login', 'session_type']]
        for key in keys_to_remove:
            request.session.pop(key, None)

    def _force_logout_and_clear_session(self, request):
        """Force logout and clear all session data"""
        if request.user.is_authenticated:
            logout(request)

        # Clear all session data
        request.session.flush()

        # Create new session
        request.session.create()


class UserPermissionMiddleware(MiddlewareMixin):
    """
    Middleware to ensure users have proper permissions
    """
    
    def process_request(self, request):
        # Skip for non-authenticated users and admin
        if not request.user.is_authenticated or request.path.startswith('/admin/'):
            return None
            
        # Skip for login/logout pages
        if request.path in [reverse('login'), reverse('logout')]:
            return None
            
        # Ensure user has basic permissions
        from .views import ensure_user_has_basic_permissions
        ensure_user_has_basic_permissions(request.user)
        
        return None
