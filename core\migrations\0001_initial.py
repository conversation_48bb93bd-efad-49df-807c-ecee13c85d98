# Generated by Django 4.2.23 on 2025-06-30 21:46

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Association',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الجمعية')),
                ('description', models.TextField(blank=True, verbose_name='وصف الجمعية')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('registration_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التسجيل')),
                ('established_date', models.DateField(verbose_name='تاريخ التأسيس')),
                ('monthly_contribution', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الاشتراك الشهري')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'جمعية',
                'verbose_name_plural': 'الجمعيات',
            },
        ),
        migrations.CreateModel(
            name='Loan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='مبلغ القرض')),
                ('interest_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='معدل الفائدة %')),
                ('duration_months', models.PositiveIntegerField(verbose_name='مدة القرض بالأشهر')),
                ('monthly_payment', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='القسط الشهري')),
                ('status', models.CharField(choices=[('pending', 'قيد المراجعة'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض'), ('active', 'نشط'), ('completed', 'مكتمل')], default='pending', max_length=20, verbose_name='الحالة')),
                ('application_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ التقديم')),
                ('approval_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('start_date', models.DateField(blank=True, null=True, verbose_name='تاريخ بداية القرض')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء القرض')),
                ('purpose', models.TextField(verbose_name='الغرض من القرض')),
                ('guarantor_name', models.CharField(max_length=100, verbose_name='اسم الضامن')),
                ('guarantor_phone', models.CharField(max_length=10, verbose_name='جوال الضامن')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'قرض',
                'verbose_name_plural': 'القروض',
                'ordering': ['-application_date'],
            },
        ),
        migrations.CreateModel(
            name='Member',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('national_id', models.CharField(max_length=10, unique=True, validators=[django.core.validators.RegexValidator(message='رقم الهوية يجب أن يكون 10 أرقام', regex='^\\d{10}$')], verbose_name='رقم الهوية')),
                ('phone', models.CharField(max_length=10, validators=[django.core.validators.RegexValidator(message='رقم الجوال يجب أن يبدأ بـ 05 ويتكون من 10 أرقام', regex='^05\\d{8}$')], verbose_name='رقم الجوال')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('suspended', 'موقوف')], default='active', max_length=20, verbose_name='الحالة')),
                ('joined_at', models.DateField(verbose_name='تاريخ الانضمام')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('association', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.association', verbose_name='الجمعية')),
            ],
            options={
                'verbose_name': 'عضو',
                'verbose_name_plural': 'الأعضاء',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('date', models.DateField(verbose_name='تاريخ الدفع')),
                ('payment_type', models.CharField(choices=[('monthly', 'اشتراك شهري'), ('penalty', 'غرامة'), ('loan_repayment', 'سداد قرض'), ('other', 'أخرى')], default='monthly', max_length=20, verbose_name='نوع الدفعة')),
                ('month', models.DateField(verbose_name='الشهر المدفوع عنه')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('receipt_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإيصال')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أضيف بواسطة')),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='core.member', verbose_name='العضو')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'الدفعات',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='LoanPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('date', models.DateField(verbose_name='تاريخ الدفع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('receipt_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإيصال')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أضيف بواسطة')),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loan_payments', to='core.loan', verbose_name='القرض')),
            ],
            options={
                'verbose_name': 'دفعة قرض',
                'verbose_name_plural': 'دفعات القروض',
                'ordering': ['-date'],
            },
        ),
        migrations.AddField(
            model_name='loan',
            name='member',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loans', to='core.member', verbose_name='العضو'),
        ),
    ]
