# Generated by Django 4.2.23 on 2025-06-30 23:39

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_association_monthly_contribution_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='member',
            name='national_id',
            field=models.CharField(max_length=9, unique=True, validators=[django.core.validators.RegexValidator(message='رقم الهوية يجب أن يكون 9 أرقام', regex='^\\d{9}$')], verbose_name='رقم الهوية'),
        ),
    ]
