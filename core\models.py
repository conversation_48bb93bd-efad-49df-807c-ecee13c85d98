from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator
from django.utils import timezone


class Association(models.Model):
    """نموذج الجمعية التعاونية"""
    name = models.Char<PERSON>ield(max_length=200, verbose_name="اسم الجمعية")
    description = models.TextField(blank=True, verbose_name="وصف الجمعية")
    address = models.TextField(verbose_name="العنوان")
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    registration_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التسجيل")
    established_date = models.DateField(verbose_name="تاريخ التأسيس")
    monthly_contribution = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الاشتراك الشهري (شيقل)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "جمعية"
        verbose_name_plural = "الجمعيات"

    def __str__(self):
        return self.name


class Member(models.Model):
    """نموذج العضو"""
    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('suspended', 'موقوف'),
    ]

    national_id_validator = RegexValidator(
        regex=r'^\d{9}$',
        message='رقم الهوية يجب أن يكون 9 أرقام'
    )

    phone_validator = RegexValidator(
        regex=r'^05\d{8}$',
        message='رقم الجوال يجب أن يبدأ بـ 05 ويتكون من 10 أرقام'
    )

    association = models.ForeignKey(Association, on_delete=models.CASCADE, verbose_name="الجمعية")
    name = models.CharField(max_length=100, verbose_name="الاسم الكامل")
    national_id = models.CharField(
        max_length=9,
        unique=True,
        validators=[national_id_validator],
        verbose_name="رقم الهوية"
    )
    phone = models.CharField(
        max_length=10,
        validators=[phone_validator],
        verbose_name="رقم الجوال"
    )
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    address = models.TextField(verbose_name="العنوان")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    joined_at = models.DateField(verbose_name="تاريخ الانضمام")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "عضو"
        verbose_name_plural = "الأعضاء"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.national_id}"

    @property
    def total_payments(self):
        """إجمالي المدفوعات"""
        return self.payments.aggregate(total=models.Sum('amount'))['total'] or 0

    @property
    def last_payment_date(self):
        """تاريخ آخر دفعة"""
        last_payment = self.payments.order_by('-date').first()
        return last_payment.date if last_payment else None


class Payment(models.Model):
    """نموذج الدفعة"""
    PAYMENT_TYPE_CHOICES = [
        ('monthly', 'اشتراك شهري'),
        ('penalty', 'غرامة'),
        ('loan_repayment', 'سداد قرض'),
        ('other', 'أخرى'),
    ]

    member = models.ForeignKey(Member, on_delete=models.CASCADE, related_name='payments', verbose_name="العضو")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="المبلغ (شيقل)")
    date = models.DateField(verbose_name="تاريخ الدفع")
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPE_CHOICES, default='monthly', verbose_name="نوع الدفعة")
    month = models.DateField(verbose_name="الشهر المدفوع عنه")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    receipt_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الإيصال")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name="أضيف بواسطة")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "دفعة"
        verbose_name_plural = "الدفعات"
        ordering = ['-date']

    def __str__(self):
        return f"{self.member.name} - {self.amount} - {self.date}"


class Loan(models.Model):
    """نموذج القرض"""
    STATUS_CHOICES = [
        ('pending', 'قيد المراجعة'),
        ('approved', 'موافق عليه'),
        ('rejected', 'مرفوض'),
        ('active', 'نشط'),
        ('completed', 'مكتمل'),
    ]

    member = models.ForeignKey(Member, on_delete=models.CASCADE, related_name='loans', verbose_name="العضو")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="مبلغ القرض (شيقل)")
    interest_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="معدل الفائدة %")
    duration_months = models.PositiveIntegerField(verbose_name="مدة القرض بالأشهر")
    monthly_payment = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="القسط الشهري (شيقل)")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="الحالة")
    application_date = models.DateField(default=timezone.now, verbose_name="تاريخ التقديم")
    approval_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الموافقة")
    start_date = models.DateField(null=True, blank=True, verbose_name="تاريخ بداية القرض")
    end_date = models.DateField(null=True, blank=True, verbose_name="تاريخ انتهاء القرض")
    purpose = models.TextField(verbose_name="الغرض من القرض")
    guarantor_name = models.CharField(max_length=100, verbose_name="اسم الضامن")
    guarantor_phone = models.CharField(max_length=10, verbose_name="جوال الضامن")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "قرض"
        verbose_name_plural = "القروض"
        ordering = ['-application_date']

    def __str__(self):
        return f"قرض {self.member.name} - {self.amount}"

    @property
    def total_amount_with_interest(self):
        """إجمالي المبلغ مع الفوائد"""
        return self.amount * (1 + self.interest_rate / 100)

    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        paid = self.loan_payments.aggregate(total=models.Sum('amount'))['total'] or 0
        return self.total_amount_with_interest - paid


class LoanPayment(models.Model):
    """نموذج دفعة القرض"""
    loan = models.ForeignKey(Loan, on_delete=models.CASCADE, related_name='loan_payments', verbose_name="القرض")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="المبلغ (شيقل)")
    date = models.DateField(verbose_name="تاريخ الدفع")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    receipt_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الإيصال")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name="أضيف بواسطة")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "دفعة قرض"
        verbose_name_plural = "دفعات القروض"
        ordering = ['-date']

    def __str__(self):
        return f"دفعة قرض {self.loan.member.name} - {self.amount}"
