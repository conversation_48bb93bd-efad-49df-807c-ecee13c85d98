"""
Custom session backends for Tadamon project to ensure admin/regular session separation
"""
from django.contrib.sessions.backends.db import SessionStore as DBSessionStore


class SessionStore(DBSessionStore):
    """
    Custom session store that ensures complete separation between admin and regular sessions
    """

    def save(self, must_create=False):
        """
        Save session with validation for session type integrity
        """
        # Get session type
        session_type = self.get('session_type', 'unknown')

        # Validate session integrity - ensure no conflicting flags
        if session_type == 'admin' and self.get('regular_login', False):
            # Admin session should not have regular login flag
            self.pop('regular_login', None)

        elif session_type == 'regular' and self.get('admin_login', False):
            # Regular session should not have admin login flag
            self.pop('admin_login', None)

        return super().save(must_create)

    def load(self):
        """
        Load session with validation for conflicting flags
        """
        data = super().load()

        # Validate session consistency
        admin_login = data.get('admin_login', False)
        regular_login = data.get('regular_login', False)

        # Check for conflicting session flags - if found, clear session
        if admin_login and regular_login:
            self.flush()
            return {}

        return data
