#!/usr/bin/env python
"""
إنشاء مجموعات صلاحيات للموظفين في منصة تضامن
"""

import os
import sys
import django
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType

def setup_django():
    """إعداد Django"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tadamon.settings')
    django.setup()

def create_staff_groups():
    """إنشاء مجموعات الموظفين مع الصلاحيات المناسبة"""
    
    print("🔧 إنشاء مجموعات الموظفين...")
    
    # مجموعة موظف الأعضاء
    members_group, created = Group.objects.get_or_create(name='موظف الأعضاء')
    if created:
        print("✅ تم إنشاء مجموعة: موظف الأعضاء")
    
    # صلاحيات الأعضاء
    member_permissions = [
        'add_member',
        'change_member', 
        'view_member',
        'view_association',
    ]
    
    for perm_code in member_permissions:
        try:
            permission = Permission.objects.get(codename=perm_code)
            members_group.permissions.add(permission)
            print(f"  ✅ أضيفت صلاحية: {permission.name}")
        except Permission.DoesNotExist:
            print(f"  ❌ صلاحية غير موجودة: {perm_code}")
    
    # مجموعة موظف المالية
    finance_group, created = Group.objects.get_or_create(name='موظف المالية')
    if created:
        print("✅ تم إنشاء مجموعة: موظف المالية")
    
    # صلاحيات المالية
    finance_permissions = [
        'add_payment',
        'change_payment',
        'view_payment',
        'view_member',
        'add_loan',
        'change_loan',
        'view_loan',
        'add_loanpayment',
        'change_loanpayment',
        'view_loanpayment',
    ]
    
    for perm_code in finance_permissions:
        try:
            permission = Permission.objects.get(codename=perm_code)
            finance_group.permissions.add(permission)
            print(f"  ✅ أضيفت صلاحية: {permission.name}")
        except Permission.DoesNotExist:
            print(f"  ❌ صلاحية غير موجودة: {perm_code}")
    
    # مجموعة مدير التقارير
    reports_group, created = Group.objects.get_or_create(name='مدير التقارير')
    if created:
        print("✅ تم إنشاء مجموعة: مدير التقارير")
    
    # صلاحيات التقارير (عرض فقط)
    reports_permissions = [
        'view_member',
        'view_payment', 
        'view_loan',
        'view_loanpayment',
        'view_association',
    ]
    
    for perm_code in reports_permissions:
        try:
            permission = Permission.objects.get(codename=perm_code)
            reports_group.permissions.add(permission)
            print(f"  ✅ أضيفت صلاحية: {permission.name}")
        except Permission.DoesNotExist:
            print(f"  ❌ صلاحية غير موجودة: {perm_code}")
    
    print("\n" + "="*50)
    print("✅ تم إنشاء جميع مجموعات الموظفين بنجاح!")
    print("="*50)
    
    print("\n📋 المجموعات المتاحة:")
    print("1. موظف الأعضاء - إدارة الأعضاء والجمعيات")
    print("2. موظف المالية - إدارة الدفعات والقروض")  
    print("3. مدير التقارير - عرض التقارير والإحصائيات")
    
    print("\n💡 كيفية الاستخدام:")
    print("1. اذهب إلى لوحة الإدارة")
    print("2. أضف مستخدم جديد")
    print("3. فعّل 'Staff status'")
    print("4. أضف المستخدم للمجموعة المناسبة")
    print("5. احفظ التغييرات")

def list_all_permissions():
    """عرض جميع الصلاحيات المتاحة"""
    print("📋 جميع الصلاحيات المتاحة في النظام:")
    print("="*50)
    
    # صلاحيات النماذج المخصصة
    from core.models import Member, Payment, Loan, LoanPayment, Association
    
    models = [Member, Payment, Loan, LoanPayment, Association]
    
    for model in models:
        content_type = ContentType.objects.get_for_model(model)
        permissions = Permission.objects.filter(content_type=content_type)
        
        print(f"\n🔹 {model._meta.verbose_name}:")
        for perm in permissions:
            print(f"  • {perm.codename} - {perm.name}")

def main():
    """الدالة الرئيسية"""
    setup_django()
    
    print("🔐 إعداد مجموعات الموظفين - منصة تضامن")
    print("="*50)
    print("1. إنشاء مجموعات الموظفين")
    print("2. عرض جميع الصلاحيات المتاحة")
    print("0. خروج")
    
    choice = input("\nاختر العملية: ").strip()
    
    if choice == '1':
        create_staff_groups()
    elif choice == '2':
        list_all_permissions()
    elif choice == '0':
        print("👋 شكراً!")
    else:
        print("❌ اختيار غير صحيح")

if __name__ == '__main__':
    main()
