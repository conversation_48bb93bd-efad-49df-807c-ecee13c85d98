#!/usr/bin/env python
"""
إصلاح جميع المستخدمين في منصة تضامن
"""

import os
import sys
import django

def setup_django():
    """إعداد Django"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tadamon.settings')
    django.setup()

# إعداد Django أولاً
setup_django()

# الآن يمكن استيراد النماذج
from django.contrib.auth.models import User, Group, Permission

def create_basic_permissions():
    """إنشاء الصلاحيات الأساسية"""
    print("🔧 إنشاء الصلاحيات الأساسية...")
    
    # إنشاء مجموعة المستخدمين العاديين
    basic_group, created = Group.objects.get_or_create(name='مستخدم عادي')
    if created:
        print("✅ تم إنشاء مجموعة: مستخدم عادي")
    
    # الصلاحيات الأساسية
    basic_permissions = [
        'view_member',
        'view_payment', 
        'view_association',
    ]
    
    # إضافة الصلاحيات للمجموعة
    for perm_code in basic_permissions:
        try:
            permission = Permission.objects.get(codename=perm_code)
            basic_group.permissions.add(permission)
            print(f"  ✅ أضيفت صلاحية: {permission.name}")
        except Permission.DoesNotExist:
            print(f"  ❌ صلاحية غير موجودة: {perm_code}")
    
    return basic_group

def fix_all_users():
    """إصلاح جميع المستخدمين"""
    print("\n🔧 إصلاح جميع المستخدمين...")
    
    # إنشاء المجموعة الأساسية
    basic_group = create_basic_permissions()
    
    # الحصول على جميع المستخدمين
    all_users = User.objects.all()
    
    print(f"\n📋 تم العثور على {all_users.count()} مستخدم:")
    
    for user in all_users:
        print(f"\n👤 معالجة المستخدم: {user.username}")
        
        # تفعيل الحساب إذا لم يكن مفعل
        if not user.is_active:
            user.is_active = True
            user.save()
            print(f"  ✅ تم تفعيل الحساب")
        
        # إعطاء صلاحيات حسب نوع المستخدم
        if user.is_superuser:
            print(f"  🔴 مدير كامل - لا يحتاج تعديل")
        elif user.is_staff:
            print(f"  🟡 موظف - لا يحتاج تعديل")
        else:
            # مستخدم عادي - إضافة للمجموعة الأساسية
            if not user.groups.filter(name='مستخدم عادي').exists():
                user.groups.add(basic_group)
                print(f"  ✅ تم إضافة للمجموعة الأساسية")
            else:
                print(f"  ℹ️  موجود في المجموعة الأساسية")

def test_user_permissions():
    """اختبار صلاحيات المستخدمين"""
    print("\n🧪 اختبار صلاحيات المستخدمين...")
    
    users = User.objects.all()
    
    for user in users:
        print(f"\n👤 {user.username}:")
        print(f"  🔐 نوع الحساب: ", end="")
        
        if user.is_superuser:
            print("مدير كامل")
        elif user.is_staff:
            print("موظف")
        else:
            print("مستخدم عادي")
        
        print(f"  📊 الحالة: {'نشط' if user.is_active else 'معطل'}")
        
        # اختبار الصلاحيات الأساسية
        can_view_members = user.has_perm('core.view_member') or user.is_staff or user.groups.filter(name='مستخدم عادي').exists()
        can_view_payments = user.has_perm('core.view_payment') or user.is_staff or user.groups.filter(name='مستخدم عادي').exists()
        
        print(f"  ✅ يمكنه عرض الأعضاء: {'نعم' if can_view_members else 'لا'}")
        print(f"  ✅ يمكنه عرض الدفعات: {'نعم' if can_view_payments else 'لا'}")
        
        # عرض المجموعات
        groups = user.groups.all()
        if groups:
            print(f"  👥 المجموعات: {', '.join([g.name for g in groups])}")

def create_test_users():
    """إنشاء مستخدمين تجريبيين"""
    print("\n🧪 إنشاء مستخدمين تجريبيين...")
    
    test_users = [
        {'username': 'test_user', 'password': 'test123', 'type': 'عادي'},
        {'username': 'test_staff', 'password': 'staff123', 'type': 'موظف'},
    ]
    
    for user_data in test_users:
        username = user_data['username']
        
        if User.objects.filter(username=username).exists():
            print(f"  ℹ️  المستخدم {username} موجود بالفعل")
            continue
        
        user = User.objects.create_user(
            username=username,
            password=user_data['password'],
            email=f"{username}@test.com"
        )
        
        if user_data['type'] == 'موظف':
            user.is_staff = True
            user.save()
        
        print(f"  ✅ تم إنشاء مستخدم {user_data['type']}: {username}")

def main():
    """الدالة الرئيسية"""
    print("🔐 إصلاح شامل للمستخدمين - منصة تضامن")
    print("="*60)
    
    try:
        # إصلاح جميع المستخدمين
        fix_all_users()
        
        # اختبار الصلاحيات
        test_user_permissions()
        
        print("\n" + "="*60)
        print("✅ تم الإصلاح الشامل بنجاح!")
        print("="*60)
        
        print("\n💡 الآن يمكن لجميع المستخدمين:")
        print("  ✅ تسجيل الدخول في الموقع العادي")
        print("  ✅ الوصول للوحة التحكم")
        print("  ✅ عرض الأعضاء والدفعات")
        
        print("\n🔗 روابط مفيدة:")
        print("  📱 الموقع العادي: http://127.0.0.1:8000")
        print("  🔧 لوحة الإدارة: http://127.0.0.1:8000/admin")
        
        print("\n👥 المستخدمين المتاحين:")
        users = User.objects.all()
        for user in users:
            user_type = "مدير كامل" if user.is_superuser else "موظف" if user.is_staff else "مستخدم عادي"
            print(f"  • {user.username} ({user_type})")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
