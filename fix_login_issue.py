#!/usr/bin/env python
"""
إصلاح مشكلة تسجيل الدخول للمستخدمين العاديين
"""

import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tadamon.settings')
django.setup()

from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType

def create_permissions():
    """إنشاء الصلاحيات المطلوبة"""
    print("🔧 إنشاء الصلاحيات المطلوبة...")
    
    # التأكد من وجود الصلاحيات
    from core.models import Member, Payment, Association
    
    # إنشاء content types إذا لم تكن موجودة
    member_ct = ContentType.objects.get_for_model(Member)
    payment_ct = ContentType.objects.get_for_model(Payment)
    association_ct = ContentType.objects.get_for_model(Association)
    
    print(f"✅ Content types created/verified")

def create_basic_group():
    """إنشاء مجموعة المستخدمين العاديين"""
    print("🔧 إنشاء مجموعة المستخدمين العاديين...")
    
    # إنشاء المجموعة
    basic_group, created = Group.objects.get_or_create(name='مستخدم عادي')
    if created:
        print("✅ تم إنشاء مجموعة: مستخدم عادي")
    else:
        print("ℹ️  مجموعة المستخدمين العاديين موجودة")
    
    # إضافة الصلاحيات
    permissions_to_add = [
        'view_member',
        'view_payment',
        'view_association',
    ]
    
    for perm_code in permissions_to_add:
        try:
            permission = Permission.objects.get(codename=perm_code)
            basic_group.permissions.add(permission)
            print(f"  ✅ أضيفت صلاحية: {permission.name}")
        except Permission.DoesNotExist:
            print(f"  ❌ صلاحية غير موجودة: {perm_code}")
    
    return basic_group

def fix_all_regular_users():
    """إصلاح جميع المستخدمين العاديين"""
    print("\n🔧 إصلاح المستخدمين العاديين...")
    
    # الحصول على المجموعة
    basic_group = Group.objects.get(name='مستخدم عادي')
    
    # العثور على المستخدمين العاديين
    regular_users = User.objects.filter(is_staff=False, is_superuser=False)
    
    print(f"📋 تم العثور على {regular_users.count()} مستخدم عادي:")
    
    for user in regular_users:
        # تفعيل الحساب
        if not user.is_active:
            user.is_active = True
            user.save()
            print(f"  ✅ تم تفعيل: {user.username}")
        
        # إضافة للمجموعة
        if not user.groups.filter(name='مستخدم عادي').exists():
            user.groups.add(basic_group)
            print(f"  ✅ تم إضافة {user.username} للمجموعة الأساسية")
        else:
            print(f"  ℹ️  {user.username} موجود في المجموعة")

def test_user_login():
    """اختبار تسجيل الدخول"""
    print("\n🧪 اختبار المستخدمين...")
    
    users = User.objects.all()
    
    for user in users:
        print(f"\n👤 {user.username}:")
        print(f"  🔐 نشط: {'نعم' if user.is_active else 'لا'}")
        print(f"  🔐 موظف: {'نعم' if user.is_staff else 'لا'}")
        print(f"  🔐 مدير: {'نعم' if user.is_superuser else 'لا'}")
        
        # التحقق من المجموعات
        groups = user.groups.all()
        if groups:
            print(f"  👥 المجموعات: {', '.join([g.name for g in groups])}")
        else:
            print(f"  👥 المجموعات: لا يوجد")
        
        # التحقق من الصلاحيات
        can_view_members = (
            user.has_perm('core.view_member') or 
            user.is_staff or 
            user.groups.filter(name='مستخدم عادي').exists()
        )
        print(f"  ✅ يمكنه عرض الأعضاء: {'نعم' if can_view_members else 'لا'}")

def create_test_user():
    """إنشاء مستخدم تجريبي"""
    print("\n🧪 إنشاء مستخدم تجريبي...")
    
    username = 'normaluser'
    
    # حذف المستخدم إذا كان موجود
    if User.objects.filter(username=username).exists():
        User.objects.get(username=username).delete()
        print(f"  🗑️  تم حذف المستخدم القديم: {username}")
    
    # إنشاء مستخدم جديد
    user = User.objects.create_user(
        username=username,
        password='normal123',
        email='<EMAIL>'
    )
    
    # إضافة للمجموعة الأساسية
    basic_group = Group.objects.get(name='مستخدم عادي')
    user.groups.add(basic_group)
    
    print(f"  ✅ تم إنشاء مستخدم تجريبي:")
    print(f"     اسم المستخدم: {username}")
    print(f"     كلمة المرور: normal123")
    print(f"     المجموعة: مستخدم عادي")

def main():
    """الدالة الرئيسية"""
    print("🔐 إصلاح مشكلة تسجيل الدخول - منصة تضامن")
    print("="*60)
    
    try:
        # إنشاء الصلاحيات
        create_permissions()
        
        # إنشاء المجموعة الأساسية
        create_basic_group()
        
        # إصلاح المستخدمين الموجودين
        fix_all_regular_users()
        
        # إنشاء مستخدم تجريبي
        create_test_user()
        
        # اختبار النتائج
        test_user_login()
        
        print("\n" + "="*60)
        print("✅ تم إصلاح مشكلة تسجيل الدخول بنجاح!")
        print("="*60)
        
        print("\n🎯 يمكنك الآن اختبار تسجيل الدخول:")
        print("  🌐 الموقع: http://127.0.0.1:8000")
        print("  👤 مستخدم تجريبي: normaluser")
        print("  🔑 كلمة المرور: normal123")
        
        print("\n💡 إذا استمرت المشكلة:")
        print("  1. تأكد من أن الخادم يعمل")
        print("  2. امسح cache المتصفح")
        print("  3. جرب في نافذة خاصة")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
