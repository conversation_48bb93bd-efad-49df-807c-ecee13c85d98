#!/usr/bin/env python
"""
إصلاح صلاحيات المستخدمين في منصة تضامن
"""

import os
import sys
import django

def setup_django():
    """إعداد Django"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tadamon.settings')
    django.setup()

# إعداد Django أولاً
setup_django()

# الآن يمكن استيراد النماذج
from django.contrib.auth.models import User, Group, Permission

def create_basic_user_group():
    """إنشاء مجموعة للمستخدمين العاديين"""
    print("🔧 إنشاء مجموعة المستخدمين العاديين...")
    
    # إنشاء مجموعة المستخدمين العاديين
    basic_group, created = Group.objects.get_or_create(name='مستخدم عادي')
    if created:
        print("✅ تم إنشاء مجموعة: مستخدم عادي")
    
    # صلاحيات أساسية للمستخدمين العاديين
    basic_permissions = [
        'view_member',      # عرض الأعضاء
        'view_payment',     # عرض الدفعات
        'view_association', # عرض بيانات الجمعية
    ]
    
    for perm_code in basic_permissions:
        try:
            permission = Permission.objects.get(codename=perm_code)
            basic_group.permissions.add(permission)
            print(f"  ✅ أضيفت صلاحية: {permission.name}")
        except Permission.DoesNotExist:
            print(f"  ❌ صلاحية غير موجودة: {perm_code}")
    
    return basic_group

def fix_regular_users():
    """إصلاح صلاحيات المستخدمين العاديين"""
    print("\n🔧 إصلاح صلاحيات المستخدمين العاديين...")
    
    # الحصول على مجموعة المستخدمين العاديين
    try:
        basic_group = Group.objects.get(name='مستخدم عادي')
    except Group.DoesNotExist:
        basic_group = create_basic_user_group()
    
    # العثور على المستخدمين العاديين (ليسوا staff أو superuser)
    regular_users = User.objects.filter(is_staff=False, is_superuser=False, is_active=True)
    
    if not regular_users.exists():
        print("❌ لا يوجد مستخدمين عاديين")
        return
    
    print(f"📋 تم العثور على {regular_users.count()} مستخدم عادي:")
    
    for user in regular_users:
        # إضافة المستخدم للمجموعة الأساسية
        user.groups.add(basic_group)
        print(f"  ✅ تم إضافة {user.username} لمجموعة المستخدمين العاديين")
    
    print("\n✅ تم إصلاح صلاحيات جميع المستخدمين العاديين!")

def list_users_and_permissions():
    """عرض قائمة المستخدمين وصلاحياتهم"""
    print("\n📋 قائمة المستخدمين وصلاحياتهم:")
    print("="*60)
    
    users = User.objects.all().order_by('username')
    
    for user in users:
        print(f"\n👤 {user.username} ({user.get_full_name() or 'بدون اسم'})")
        print(f"   📧 {user.email or 'بدون إيميل'}")
        print(f"   🔐 نوع الحساب: ", end="")
        
        if user.is_superuser:
            print("مدير كامل")
        elif user.is_staff:
            print("موظف")
        else:
            print("مستخدم عادي")
        
        print(f"   📊 الحالة: {'نشط' if user.is_active else 'معطل'}")
        
        # عرض المجموعات
        groups = user.groups.all()
        if groups:
            print(f"   👥 المجموعات: {', '.join([g.name for g in groups])}")
        else:
            print("   👥 المجموعات: لا يوجد")
        
        # عرض الصلاحيات المباشرة
        user_perms = user.user_permissions.all()
        if user_perms:
            print(f"   🔑 صلاحيات مباشرة: {user_perms.count()}")
        
        print("   " + "-"*50)

def give_basic_permissions_to_user(username):
    """إعطاء صلاحيات أساسية لمستخدم محدد"""
    try:
        user = User.objects.get(username=username)
    except User.DoesNotExist:
        print(f"❌ المستخدم '{username}' غير موجود")
        return False
    
    print(f"🔧 إعطاء صلاحيات أساسية للمستخدم: {user.username}")
    
    # التأكد من أن المستخدم نشط
    if not user.is_active:
        user.is_active = True
        user.save()
        print("  ✅ تم تفعيل الحساب")
    
    # إضافة للمجموعة الأساسية
    try:
        basic_group = Group.objects.get(name='مستخدم عادي')
        user.groups.add(basic_group)
        print("  ✅ تم إضافة المستخدم لمجموعة المستخدمين العاديين")
    except Group.DoesNotExist:
        print("  ❌ مجموعة المستخدمين العاديين غير موجودة")
        print("  💡 قم بتشغيل الخيار 1 أولاً لإنشاء المجموعات")
        return False
    
    print(f"  ✅ تم إعطاء صلاحيات أساسية للمستخدم: {user.username}")
    return True

def main():
    """الدالة الرئيسية"""
    
    while True:
        print("\n" + "="*60)
        print("🔐 إصلاح صلاحيات المستخدمين - منصة تضامن")
        print("="*60)
        print("1. إنشاء مجموعة المستخدمين العاديين")
        print("2. إصلاح صلاحيات جميع المستخدمين العاديين")
        print("3. إعطاء صلاحيات أساسية لمستخدم محدد")
        print("4. عرض قائمة المستخدمين وصلاحياتهم")
        print("5. إصلاح شامل (تشغيل جميع الخطوات)")
        print("0. خروج")
        print("-" * 60)
        
        choice = input("اختر العملية: ").strip()
        
        if choice == '1':
            create_basic_user_group()
        elif choice == '2':
            fix_regular_users()
        elif choice == '3':
            username = input("أدخل اسم المستخدم: ").strip()
            if username:
                give_basic_permissions_to_user(username)
            else:
                print("❌ اسم المستخدم مطلوب")
        elif choice == '4':
            list_users_and_permissions()
        elif choice == '5':
            print("🔧 تشغيل الإصلاح الشامل...")
            create_basic_user_group()
            fix_regular_users()
            print("\n✅ تم الإصلاح الشامل بنجاح!")
        elif choice == '0':
            print("👋 شكراً لاستخدام منصة تضامن!")
            break
        else:
            print("❌ اختيار غير صحيح")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == '__main__':
    main()
