#!/usr/bin/env python
"""
Database setup script for Tadamon Cooperative Management Platform
This script will create the database, run migrations, and create a superuser
"""

import os
import sys
import django
from django.core.management import execute_from_command_line
from django.contrib.auth import get_user_model

def setup_database():
    """Setup the database with initial data"""
    
    print("🔧 Setting up Tadamon Database...")
    
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tadamon.settings')
    django.setup()
    
    print("📊 Creating database tables...")
    execute_from_command_line(['manage.py', 'makemigrations'])
    execute_from_command_line(['manage.py', 'migrate'])
    
    print("👤 Creating superuser...")
    User = get_user_model()
    
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print("✅ Superuser created: username=admin, password=admin123")
    else:
        print("ℹ️  Superuser already exists")
    
    print("🏢 Creating default association...")
    from core.models import Association
    from django.utils import timezone
    
    if not Association.objects.exists():
        Association.objects.create(
            name='جمعية تضامن التعاونية',
            description='جمعية تعاونية لخدمة المجتمع المحلي',
            address='فلسطين',
            phone='0500000000',
            email='<EMAIL>',
            registration_number='TADAMON001',
            established_date=timezone.now().date(),
            monthly_contribution=50.00
        )
        print("✅ Default association created")
    else:
        print("ℹ️  Association already exists")
    
    print("🎉 Database setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Run: python manage.py runserver")
    print("2. Open: http://127.0.0.1:8000")
    print("3. Admin panel: http://127.0.0.1:8000/admin")
    print("4. Login with: admin / admin123")

if __name__ == '__main__':
    setup_database()
