/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* Root Variables */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.75rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* Base Styles */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    direction: rtl;
    text-align: right;
}

/* Card Styles */
.card {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    color: white;
    border-bottom: none;
    font-weight: 600;
}

/* Gradient Backgrounds */
.bg-gradient-primary {
    background: linear-gradient(45deg, var(--primary-color), #0056b3) !important;
}

.bg-gradient-success {
    background: linear-gradient(45deg, var(--success-color), #146c43) !important;
}

.bg-gradient-info {
    background: linear-gradient(45deg, var(--info-color), #0aa2c0) !important;
}

.bg-gradient-warning {
    background: linear-gradient(45deg, var(--warning-color), #d39e00) !important;
}

.bg-gradient-danger {
    background: linear-gradient(45deg, var(--danger-color), #b02a37) !important;
}

/* Table Styles */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table th, .table td {
    vertical-align: middle;
    border-color: #dee2e6;
    padding: 1rem 0.75rem;
}

.table-dark th {
    background: linear-gradient(45deg, var(--dark-color), #495057);
    border-color: #495057;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.01);
    transition: var(--transition);
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.5rem 1.5rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #146c43);
}

.btn-info {
    background: linear-gradient(45deg, var(--info-color), #0aa2c0);
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #d39e00);
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #b02a37);
}

/* Form Styles */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
    padding: 0.75rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    transform: scale(1.02);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Navigation Styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(45deg, var(--primary-color), #0056b3) !important;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Badge Styles */
.badge {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

/* Pagination Styles */
.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 0.125rem;
    border: none;
    color: var(--primary-color);
    transition: var(--transition);
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    border: none;
}

/* Animation Classes */
.animate__fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.opacity-75 {
    opacity: 0.75;
}

.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .display-4 {
        font-size: 2rem;
    }

    .display-6 {
        font-size: 1.5rem;
    }
}

/* Print Styles */
@media print {
    .navbar, .btn, .pagination {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }

    body {
        background: white;
    }
}
