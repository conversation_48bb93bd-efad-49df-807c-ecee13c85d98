/**
 * Tadamon Cooperative Management Platform
 * Main JavaScript File
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });

    // Form validation enhancements
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // Search functionality
    const searchInputs = document.querySelectorAll('input[type="search"], input[name="search"]');
    searchInputs.forEach(function(input) {
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                // Auto-submit search form after 500ms of no typing
                const form = input.closest('form');
                if (form && input.value.length >= 2) {
                    form.submit();
                }
            }, 500);
        });
    });

    // Number formatting for Arabic locale
    const numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            if (this.value) {
                const number = parseFloat(this.value);
                if (!isNaN(number)) {
                    this.value = number.toFixed(2);
                }
            }
        });
    });

    // Phone number formatting
    const phoneInputs = document.querySelectorAll('input[type="tel"], input[name*="phone"]');
    phoneInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            // Remove non-digits
            let value = this.value.replace(/\D/g, '');
            
            // Ensure it starts with 05 for Saudi numbers
            if (value.length > 0 && !value.startsWith('05')) {
                if (value.startsWith('5')) {
                    value = '0' + value;
                } else if (!value.startsWith('0')) {
                    value = '05' + value;
                }
            }
            
            // Limit to 10 digits
            if (value.length > 10) {
                value = value.substring(0, 10);
            }
            
            this.value = value;
        });
    });

    // National ID validation
    const nationalIdInputs = document.querySelectorAll('input[name*="national_id"]');
    nationalIdInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            // Remove non-digits
            let value = this.value.replace(/\D/g, '');
            
            // Limit to 9 digits
            if (value.length > 9) {
                value = value.substring(0, 9);
            }

            this.value = value;

            // Validate national ID (simple 9-digit validation)
            if (value.length === 9) {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.setCustomValidity('رقم الهوية يجب أن يكون 9 أرقام');
                this.classList.remove('is-valid');
                if (value.length > 0) {
                    this.classList.add('is-invalid');
                }
            }
        });
    });

    // Confirm delete functionality
    window.confirmDelete = function(itemName, deleteUrl) {
        if (confirm(`هل أنت متأكد من حذف "${itemName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = deleteUrl;
            
            // Add CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = csrfToken.value;
                form.appendChild(csrfInput);
            }
            
            document.body.appendChild(form);
            form.submit();
        }
    };

    // Loading states for buttons (excluding login form)
    const submitButtons = document.querySelectorAll('button[type="submit"]:not(.login-btn)');
    submitButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const form = this.closest('form');
            if (form && form.checkValidity()) {
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';

                // Re-enable after 10 seconds as fallback
                setTimeout(() => {
                    this.disabled = false;
                    this.innerHTML = this.getAttribute('data-original-text') || 'حفظ';
                }, 10000);
            }
        });

        // Store original text
        button.setAttribute('data-original-text', button.innerHTML);
    });

    // Special handling for login form
    const loginForm = document.querySelector('form[action*="login"]');
    if (loginForm) {
        const loginButton = loginForm.querySelector('button[type="submit"]');
        if (loginButton) {
            loginButton.classList.add('login-btn');
            loginForm.addEventListener('submit', function(e) {
                // Don't prevent default submission
                loginButton.disabled = true;
                loginButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';

                // Re-enable after 5 seconds as fallback
                setTimeout(() => {
                    loginButton.disabled = false;
                    loginButton.innerHTML = 'دخول';
                }, 5000);
            });
        }
    }

    // Auto-resize textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(function(textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });

    // Print functionality
    window.printPage = function() {
        window.print();
    };

    // Export functionality with loading state
    const exportLinks = document.querySelectorAll('a[href*="export"]');
    exportLinks.forEach(function(link) {
        link.addEventListener('click', function() {
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التصدير...';
            
            setTimeout(() => {
                this.innerHTML = originalText;
            }, 3000);
        });
    });

    // Dark mode toggle (if needed in future)
    window.toggleDarkMode = function() {
        document.body.classList.toggle('dark-mode');
        localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
    };

    // Load dark mode preference
    if (localStorage.getItem('darkMode') === 'true') {
        document.body.classList.add('dark-mode');
    }

});

/**
 * Validate National ID (9 digits)
 */
function validateSaudiNationalId(id) {
    // Simple validation for 9-digit national ID
    return /^\d{9}$/.test(id);
}

/**
 * Format currency for display
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('he-IL', {
        style: 'currency',
        currency: 'ILS'
    }).format(amount);
}

/**
 * Format date for Arabic locale
 */
function formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA').format(new Date(date));
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
