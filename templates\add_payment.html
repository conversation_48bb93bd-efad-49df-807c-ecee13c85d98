{% extends 'base.html' %}
{% load django_bootstrap5 %}

{% block title %}إضافة دفعة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">إضافة دفعة جديدة</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="member" class="form-label">العضو *</label>
                            <select class="form-select" id="member" name="member" required>
                                <option value="">اختر العضو</option>
                                {% for member in members %}
                                <option value="{{ member.id }}">{{ member.name }} - {{ member.national_id }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="amount" class="form-label">المبلغ (شيقل) *</label>
                            <input type="number" class="form-control" id="amount" name="amount" 
                                   step="0.01" min="0" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date" class="form-label">تاريخ الدفع *</label>
                            <input type="date" class="form-control" id="date" name="date" 
                                   value="{% now 'Y-m-d' %}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="payment_type" class="form-label">نوع الدفعة *</label>
                            <select class="form-select" id="payment_type" name="payment_type" required>
                                {% for value, label in payment_type_choices %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="month" class="form-label">الشهر المدفوع عنه *</label>
                        <input type="month" class="form-control" id="month" name="month" 
                               value="{% now 'Y-m' %}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> إضافة الدفعة
                        </button>
                        <a href="{% url 'payments' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-set month field when payment type is monthly
    const paymentTypeSelect = document.getElementById('payment_type');
    const monthInput = document.getElementById('month');
    
    paymentTypeSelect.addEventListener('change', function() {
        if (this.value === 'monthly') {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            monthInput.value = `${year}-${month}`;
        }
    });
    
    // Member search functionality
    const memberSelect = document.getElementById('member');
    const memberOptions = Array.from(memberSelect.options);
    
    // Add search input
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'form-control mb-2';
    searchInput.placeholder = 'ابحث عن العضو...';
    memberSelect.parentNode.insertBefore(searchInput, memberSelect);
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        memberSelect.innerHTML = '<option value="">اختر العضو</option>';
        
        memberOptions.slice(1).forEach(option => {
            if (option.textContent.toLowerCase().includes(searchTerm)) {
                memberSelect.appendChild(option.cloneNode(true));
            }
        });
    });
});
</script>
{% endblock %}
