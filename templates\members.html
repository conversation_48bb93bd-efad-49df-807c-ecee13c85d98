{% extends 'base.html' %}
{% load django_bootstrap5 %}

{% block title %}الأعضاء{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>إدارة الأعضاء</h2>
    <a href="{% url 'add_member' %}" class="btn btn-primary">
        <i class="fas fa-plus"></i> إضافة عضو جديد
    </a>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <input type="text" class="form-control" name="search"
                       value="{{ search_query }}" placeholder="البحث بالاسم أو رقم الهوية أو الجوال">
            </div>
            <div class="col-md-3">
                <select class="form-select" name="status">
                    <option value="">جميع الحالات</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search"></i> بحث
                </button>
            </div>
            <div class="col-md-3">
                <a href="{% url 'export_members_csv' %}" class="btn btn-outline-success w-100">
                    <i class="fas fa-download"></i> تصدير CSV
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Members Table -->
<div class="card">
    <div class="card-body">
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>الاسم</th>
                        <th>رقم الهوية</th>
                        <th>رقم الجوال</th>
                        <th>الحالة</th>
                        <th>تاريخ الانضمام</th>
                        <th>إجمالي المدفوعات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for member in page_obj %}
                    <tr>
                        <td>
                            <strong>{{ member.name }}</strong>
                            {% if member.email %}
                            <br><small class="text-muted">{{ member.email }}</small>
                            {% endif %}
                        </td>
                        <td>{{ member.national_id }}</td>
                        <td>{{ member.phone }}</td>
                        <td>
                            {% if member.status == 'active' %}
                                <span class="badge bg-success">{{ member.get_status_display }}</span>
                            {% elif member.status == 'inactive' %}
                                <span class="badge bg-secondary">{{ member.get_status_display }}</span>
                            {% else %}
                                <span class="badge bg-danger">{{ member.get_status_display }}</span>
                            {% endif %}
                        </td>
                        <td>{{ member.joined_at }}</td>
                        <td>
                            <strong>{{ member.total_payments|floatformat:2 }} شيقل</strong>
                            {% if member.last_payment_date %}
                            <br><small class="text-muted">آخر دفعة: {{ member.last_payment_date }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'edit_member' member.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="confirmDelete('{{ member.name }}', '{% url 'delete_member' member.id %}')">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5>لا توجد أعضاء</h5>
            <p class="text-muted">ابدأ بإضافة أول عضو في الجمعية</p>
            <a href="{% url 'add_member' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة عضو جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
