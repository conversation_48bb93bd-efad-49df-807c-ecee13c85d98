{% extends 'base.html' %}
{% load django_bootstrap5 %}

{% block title %}الإعدادات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">إعدادات الجمعية</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الجمعية *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ association.name }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف *</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ association.phone }}" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الجمعية</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ association.description }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان *</label>
                        <textarea class="form-control" id="address" name="address" rows="3" required>{{ association.address }}</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ association.email }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="monthly_contribution" class="form-label">الاشتراك الشهري (شيقل) *</label>
                            <input type="number" class="form-control" id="monthly_contribution" 
                                   name="monthly_contribution" step="0.01" min="0" 
                                   value="{{ association.monthly_contribution }}" required>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-start">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- System Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">معلومات النظام</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>حالة Firebase</h6>
                        {% if firebase_enabled %}
                            <span class="badge bg-success">متصل</span>
                            <p class="text-muted small mt-2">
                                النظام متصل بـ Firebase وسيتم مزامنة البيانات تلقائياً.
                            </p>
                        {% else %}
                            <span class="badge bg-warning">غير متصل</span>
                            <p class="text-muted small mt-2">
                                النظام يعمل بالبيانات المحلية فقط. لتفعيل Firebase، تأكد من وجود ملف firebase_config.json صحيح.
                            </p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6>إحصائيات سريعة</h6>
                        <ul class="list-unstyled">
                            <li><strong>إجمالي الأعضاء:</strong> {{ total_members|default:0 }}</li>
                            <li><strong>إجمالي الدفعات:</strong> {{ total_payments|default:0 }}</li>
                            <li><strong>تاريخ التأسيس:</strong> {{ association.established_date }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Backup & Export -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">النسخ الاحتياطي والتصدير</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>تصدير البيانات</h6>
                        <div class="d-grid gap-2">
                            <a href="{% url 'export_members_csv' %}" class="btn btn-outline-primary">
                                <i class="fas fa-download"></i> تصدير الأعضاء (CSV)
                            </a>
                            <a href="{% url 'export_payments_csv' %}" class="btn btn-outline-primary">
                                <i class="fas fa-download"></i> تصدير الدفعات (CSV)
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات إضافية</h6>
                        <p class="text-muted small">
                            يمكنك تصدير البيانات بصيغة CSV لاستخدامها في برامج أخرى مثل Excel.
                            الملفات المصدرة تدعم اللغة العربية بشكل كامل.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-format phone number
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        this.value = value;
    });
    
    // Validate monthly contribution
    const contributionInput = document.getElementById('monthly_contribution');
    contributionInput.addEventListener('input', function() {
        const value = parseFloat(this.value);
        if (value < 0) {
            this.value = 0;
        }
    });
});
</script>
{% endblock %}
