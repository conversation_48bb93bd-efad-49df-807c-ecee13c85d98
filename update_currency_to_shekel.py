#!/usr/bin/env python
"""
تحديث العملة من الريال إلى الشيقل في منصة تضامن
"""

import os
import sys
import django

def setup_django():
    """إعداد Django"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tadamon.settings')
    django.setup()

# إعداد Django أولاً
setup_django()

# الآن يمكن استيراد النماذج
from core.models import Association, Member, Payment, Loan, LoanPayment
from django.utils import timezone

def convert_sar_to_ils(amount_sar):
    """تحويل من الريال السعودي إلى الشيقل الإسرائيلي"""
    # سعر الصرف التقريبي: 1 ريال سعودي = 1.0 شيقل (يمكن تعديله)
    exchange_rate = 1.0
    return round(amount_sar * exchange_rate, 2)

def update_associations():
    """تحديث بيانات الجمعيات"""
    print("🔄 تحديث بيانات الجمعيات...")
    
    associations = Association.objects.all()
    updated_count = 0
    
    for association in associations:
        old_amount = association.monthly_contribution
        new_amount = convert_sar_to_ils(old_amount)
        
        if old_amount != new_amount:
            association.monthly_contribution = new_amount
            association.save()
            updated_count += 1
            print(f"  ✅ {association.name}: {old_amount} ريال → {new_amount} شيقل")
        else:
            print(f"  ℹ️  {association.name}: {old_amount} شيقل (لا يحتاج تحديث)")
    
    print(f"📊 تم تحديث {updated_count} جمعية")
    return updated_count

def update_payments():
    """تحديث بيانات الدفعات"""
    print("\n🔄 تحديث بيانات الدفعات...")
    
    payments = Payment.objects.all()
    updated_count = 0
    
    for payment in payments:
        old_amount = payment.amount
        new_amount = convert_sar_to_ils(old_amount)
        
        if old_amount != new_amount:
            payment.amount = new_amount
            payment.save()
            updated_count += 1
            if updated_count <= 5:  # عرض أول 5 فقط
                print(f"  ✅ {payment.member.name}: {old_amount} ريال → {new_amount} شيقل")
        
    if updated_count > 5:
        print(f"  ... و {updated_count - 5} دفعة أخرى")
    
    print(f"📊 تم تحديث {updated_count} دفعة")
    return updated_count

def update_loans():
    """تحديث بيانات القروض"""
    print("\n🔄 تحديث بيانات القروض...")
    
    loans = Loan.objects.all()
    updated_count = 0
    
    for loan in loans:
        old_amount = loan.amount
        old_monthly = loan.monthly_payment
        
        new_amount = convert_sar_to_ils(old_amount)
        new_monthly = convert_sar_to_ils(old_monthly)
        
        if old_amount != new_amount or old_monthly != new_monthly:
            loan.amount = new_amount
            loan.monthly_payment = new_monthly
            loan.save()
            updated_count += 1
            print(f"  ✅ قرض {loan.member.name}: {old_amount} ريال → {new_amount} شيقل")
    
    print(f"📊 تم تحديث {updated_count} قرض")
    return updated_count

def update_loan_payments():
    """تحديث دفعات القروض"""
    print("\n🔄 تحديث دفعات القروض...")
    
    loan_payments = LoanPayment.objects.all()
    updated_count = 0
    
    for loan_payment in loan_payments:
        old_amount = loan_payment.amount
        new_amount = convert_sar_to_ils(old_amount)
        
        if old_amount != new_amount:
            loan_payment.amount = new_amount
            loan_payment.save()
            updated_count += 1
            if updated_count <= 5:  # عرض أول 5 فقط
                print(f"  ✅ دفعة قرض {loan_payment.loan.member.name}: {old_amount} ريال → {new_amount} شيقل")
    
    if updated_count > 5:
        print(f"  ... و {updated_count - 5} دفعة قرض أخرى")
    
    print(f"📊 تم تحديث {updated_count} دفعة قرض")
    return updated_count

def show_statistics():
    """عرض إحصائيات العملة الجديدة"""
    print("\n📊 إحصائيات العملة الجديدة (بالشيقل):")
    print("="*50)
    
    # إحصائيات الجمعيات
    associations = Association.objects.all()
    if associations:
        total_monthly = sum(a.monthly_contribution for a in associations)
        print(f"💰 إجمالي الاشتراكات الشهرية: {total_monthly:,.2f} شيقل")
    
    # إحصائيات الدفعات
    payments = Payment.objects.all()
    if payments:
        total_payments = sum(p.amount for p in payments)
        avg_payment = total_payments / len(payments)
        print(f"💳 إجمالي الدفعات: {total_payments:,.2f} شيقل")
        print(f"📈 متوسط الدفعة: {avg_payment:,.2f} شيقل")
    
    # إحصائيات القروض
    loans = Loan.objects.all()
    if loans:
        total_loans = sum(l.amount for l in loans)
        print(f"🏦 إجمالي القروض: {total_loans:,.2f} شيقل")

def backup_data():
    """عمل نسخة احتياطية من البيانات"""
    print("💾 عمل نسخة احتياطية...")
    
    try:
        import json
        from django.core import serializers
        
        # تصدير البيانات
        data = {
            'associations': list(Association.objects.values()),
            'members': list(Member.objects.values()),
            'payments': list(Payment.objects.values()),
            'loans': list(Loan.objects.values()),
            'loan_payments': list(LoanPayment.objects.values()),
        }
        
        # حفظ النسخة الاحتياطية
        backup_file = f"backup_before_currency_change_{timezone.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ تم حفظ النسخة الاحتياطية: {backup_file}")
        return backup_file
        
    except Exception as e:
        print(f"❌ فشل في عمل النسخة الاحتياطية: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("💱 تحديث العملة من الريال إلى الشيقل - منصة تضامن")
    print("="*60)
    
    # عرض تحذير
    print("⚠️  تحذير: هذه العملية ستغير جميع المبالغ في قاعدة البيانات")
    print("💾 سيتم عمل نسخة احتياطية أولاً")
    
    confirm = input("\nهل تريد المتابعة؟ (نعم/لا): ").strip().lower()
    if confirm not in ['نعم', 'yes', 'y']:
        print("❌ تم إلغاء العملية")
        return
    
    try:
        # عمل نسخة احتياطية
        backup_file = backup_data()
        if not backup_file:
            print("❌ فشل في عمل النسخة الاحتياطية. تم إلغاء العملية.")
            return
        
        # تحديث البيانات
        total_updated = 0
        total_updated += update_associations()
        total_updated += update_payments()
        total_updated += update_loans()
        total_updated += update_loan_payments()
        
        # عرض النتائج
        print("\n" + "="*60)
        print("✅ تم تحديث العملة بنجاح!")
        print("="*60)
        print(f"📊 إجمالي العناصر المحدثة: {total_updated}")
        print(f"💾 النسخة الاحتياطية: {backup_file}")
        
        # عرض الإحصائيات الجديدة
        show_statistics()
        
        print("\n💡 ملاحظات مهمة:")
        print("  • تم تحويل جميع المبالغ من الريال إلى الشيقل")
        print("  • تم حفظ نسخة احتياطية من البيانات القديمة")
        print("  • يمكنك الآن استخدام النظام بالعملة الجديدة")
        print("  • تأكد من تحديث أي تقارير أو مستندات خارجية")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء التحديث: {e}")
        print("💡 يمكنك استعادة البيانات من النسخة الاحتياطية إذا لزم الأمر")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
